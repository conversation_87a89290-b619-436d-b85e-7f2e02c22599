package org.ruoyi.core.debtConversion.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.ruoyi.core.debtConversion.utils.AESEncrypted;
import org.springframework.security.core.authority.SimpleGrantedAuthority;

import java.util.Date;
import java.util.List;

/**
 * 债转用户对象 dc_dept_user
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
public class DebtUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 姓名 */
    @Excel(name = "姓名")
    private String name;

    /** 手机号 */
    @Excel(name = "手机号")
    @AESEncrypted(key = "CustomKey123", enableFuzzySearch = true, format = AESEncrypted.EncryptionFormat.HEX)
    private String phoneNum;

    /** 身份证 */
    @Excel(name = "身份证")
    private String idCard;

    /** 注册公司(担保公司) */
    @Excel(name = "注册公司(担保公司)")
    private Long custId;

    /** 注册公司(担保公司简称) */
    private String custName;

    /** 来源 1.小程序 */
    @Excel(name = "来源 1.小程序")
    private String dataSource;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    private String token;

    private long loginTime;

    private long expireTime;

    private List<SimpleGrantedAuthority> authorities;
    /**
     * 微信小程序code。(通过code获取唯一标识openId)
     */
    private String code;

    private String openId;

    private List<String> idCardList;

    private String realIdCard;

    private String realPhoneNum;
}
