package org.ruoyi.core.debtConversion.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

/**
 * MySQL HEX格式支持工具类
 * 提供与MySQL HEX函数兼容的加解密功能
 * 
 * <AUTHOR>
 * @date 2024
 */
public class MySQLHexUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(MySQLHexUtils.class);
    
    /**
     * 使用MySQL HEX格式加密字符串
     * 
     * @param plainText 明文
     * @return HEX格式的加密字符串
     */
    public static String encryptToHex(String plainText) {
        return AESEncryptionUtils.encrypt(plainText, "", AESEncrypted.EncryptionFormat.HEX);
    }
    
    /**
     * 使用MySQL HEX格式加密字符串
     * 
     * @param plainText 明文
     * @param key 加密密钥
     * @return HEX格式的加密字符串
     */
    public static String encryptToHex(String plainText, String key) {
        return AESEncryptionUtils.encrypt(plainText, key, AESEncrypted.EncryptionFormat.HEX);
    }
    
    /**
     * 解密HEX格式的加密字符串
     * 
     * @param hexEncryptedText HEX格式的加密文本
     * @return 解密后的明文
     */
    public static String decryptFromHex(String hexEncryptedText) {
        return AESEncryptionUtils.decrypt(hexEncryptedText);
    }
    
    /**
     * 解密HEX格式的加密字符串
     *
     * @param hexEncryptedText HEX格式的加密文本
     * @param key 解密密钥
     * @return 解密后的明文
     */
    public static String decryptFromHex(String hexEncryptedText, String key) {
        return AESEncryptionUtils.decrypt(hexEncryptedText, key);
    }

    /**
     * 解密纯HEX字符串（数据库存储格式）
     *
     * @param pureHexString 纯HEX字符串（不带HEX:前缀）
     * @return 解密后的明文
     */
    public static String decryptFromPureHex(String pureHexString) {
        return decryptFromPureHex(pureHexString, "");
    }

    /**
     * 解密纯HEX字符串（数据库存储格式）
     *
     * @param pureHexString 纯HEX字符串（不带HEX:前缀）
     * @param key 解密密钥
     * @return 解密后的明文
     */
    public static String decryptFromPureHex(String pureHexString, String key) {
        if (!StringUtils.hasText(pureHexString)) {
            return pureHexString;
        }

        // 如果是有效的HEX字符串且没有前缀，添加前缀后解密
        if (isValidHexString(pureHexString) && !pureHexString.startsWith("HEX:")) {
            return AESEncryptionUtils.decrypt("HEX:" + pureHexString, key);
        }

        // 否则直接解密
        return AESEncryptionUtils.decrypt(pureHexString, key);
    }
    
    /**
     * 判断字符串是否为HEX格式的加密数据
     * 
     * @param text 待判断的字符串
     * @return 是否为HEX格式加密数据
     */
    public static boolean isHexEncrypted(String text) {
        if (!StringUtils.hasText(text)) {
            return false;
        }
        
        AESEncrypted.EncryptionFormat format = AESEncryptionUtils.getEncryptionFormat(text);
        return format == AESEncrypted.EncryptionFormat.HEX;
    }
    
    /**
     * 生成MySQL兼容的HEX查询条件
     * 用于在SQL中直接使用HEX格式进行查询
     *
     * @param plainText 明文查询条件
     * @param key 加密密钥
     * @return MySQL HEX查询条件
     */
    public static String generateHexQueryCondition(String plainText, String key) {
        if (!StringUtils.hasText(plainText)) {
            return null;
        }

        try {
            String hexEncrypted = encryptToHex(plainText, key);
            if (hexEncrypted.startsWith("HEX:")) {
                // 移除前缀，返回纯HEX字符串（与数据库存储格式一致）
                String hexData = hexEncrypted.substring(4);
                return "'" + hexData + "'"; // 直接返回纯HEX字符串，用单引号包围
            }
            return null;
        } catch (Exception e) {
            logger.error("生成HEX查询条件失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成MySQL 0x格式的HEX查询条件
     * 用于在SQL中使用0x前缀的HEX格式进行查询
     *
     * @param plainText 明文查询条件
     * @param key 加密密钥
     * @return MySQL 0x格式HEX查询条件
     */
    public static String generateMySQLHexCondition(String plainText, String key) {
        if (!StringUtils.hasText(plainText)) {
            return null;
        }

        try {
            String hexEncrypted = encryptToHex(plainText, key);
            if (hexEncrypted.startsWith("HEX:")) {
                // 移除前缀，返回0x格式
                String hexData = hexEncrypted.substring(4);
                return "0x" + hexData; // MySQL 0x格式
            }
            return null;
        } catch (Exception e) {
            logger.error("生成MySQL HEX条件失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 批量转换为HEX格式
     * 
     * @param plainTexts 明文数组
     * @return HEX格式加密数组
     */
    public static String[] batchEncryptToHex(String... plainTexts) {
        if (plainTexts == null) {
            return null;
        }
        
        String[] result = new String[plainTexts.length];
        for (int i = 0; i < plainTexts.length; i++) {
            result[i] = encryptToHex(plainTexts[i]);
        }
        return result;
    }
    
    /**
     * 批量从HEX格式解密
     * 
     * @param hexEncryptedTexts HEX格式加密数组
     * @return 解密后的明文数组
     */
    public static String[] batchDecryptFromHex(String... hexEncryptedTexts) {
        if (hexEncryptedTexts == null) {
            return null;
        }
        
        String[] result = new String[hexEncryptedTexts.length];
        for (int i = 0; i < hexEncryptedTexts.length; i++) {
            result[i] = decryptFromHex(hexEncryptedTexts[i]);
        }
        return result;
    }
    
    /**
     * 转换Base64格式加密数据为HEX格式
     * 
     * @param base64EncryptedText Base64格式的加密文本
     * @param key 密钥
     * @return HEX格式的加密文本
     */
    public static String convertBase64ToHex(String base64EncryptedText, String key) {
        if (!StringUtils.hasText(base64EncryptedText)) {
            return base64EncryptedText;
        }
        
        try {
            // 先解密
            String plainText = AESEncryptionUtils.decrypt(base64EncryptedText, key);
            // 再用HEX格式加密
            return encryptToHex(plainText, key);
        } catch (Exception e) {
            logger.error("Base64转HEX失败: {}", e.getMessage(), e);
            return base64EncryptedText;
        }
    }
    
    /**
     * 转换HEX格式加密数据为Base64格式
     * 
     * @param hexEncryptedText HEX格式的加密文本
     * @param key 密钥
     * @return Base64格式的加密文本
     */
    public static String convertHexToBase64(String hexEncryptedText, String key) {
        if (!StringUtils.hasText(hexEncryptedText)) {
            return hexEncryptedText;
        }
        
        try {
            // 先解密
            String plainText = AESEncryptionUtils.decrypt(hexEncryptedText, key);
            // 再用Base64格式加密
            return AESEncryptionUtils.encrypt(plainText, key, AESEncrypted.EncryptionFormat.BASE64);
        } catch (Exception e) {
            logger.error("HEX转Base64失败: {}", e.getMessage(), e);
            return hexEncryptedText;
        }
    }
    
    /**
     * 验证HEX格式的有效性
     * 
     * @param hexString HEX字符串
     * @return 是否为有效的HEX格式
     */
    public static boolean isValidHexString(String hexString) {
        if (!StringUtils.hasText(hexString)) {
            return false;
        }
        
        // 移除可能的前缀
        String cleanHex = hexString;
        if (hexString.startsWith("HEX:")) {
            cleanHex = hexString.substring(4);
        } else if (hexString.startsWith("0x") || hexString.startsWith("0X")) {
            cleanHex = hexString.substring(2);
        }
        
        // 检查长度是否为偶数
        if (cleanHex.length() % 2 != 0) {
            return false;
        }
        
        // 检查是否只包含十六进制字符
        return cleanHex.matches("^[0-9A-Fa-f]+$");
    }
    
    /**
     * 清理HEX字符串，移除前缀和空格
     * 
     * @param hexString 原始HEX字符串
     * @return 清理后的HEX字符串
     */
    public static String cleanHexString(String hexString) {
        if (!StringUtils.hasText(hexString)) {
            return hexString;
        }
        
        String cleaned = hexString.trim().toUpperCase();
        
        if (cleaned.startsWith("HEX:")) {
            cleaned = cleaned.substring(4);
        } else if (cleaned.startsWith("0X")) {
            cleaned = cleaned.substring(2);
        }
        
        return cleaned;
    }
}
