package org.ruoyi.core.debtConversion.utils;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * AES加解密类型处理器
 * 用于MyBatis在数据库读写时自动进行AES加解密
 * 
 * <AUTHOR>
 * @date 2024
 */
@MappedTypes(String.class)
@MappedJdbcTypes(JdbcType.VARCHAR)
public class AESTypeHandler extends BaseTypeHandler<String> {
    
    private static final Logger logger = LoggerFactory.getLogger(AESTypeHandler.class);
    
    /**
     * 设置参数时进行加密
     * 
     * @param ps 预处理语句
     * @param i 参数索引
     * @param parameter 参数值
     * @param jdbcType JDBC类型
     * @throws SQLException SQL异常
     */
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, String parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 直接存储参数值（FieldEncryptionHelper已经处理了加密和格式）
            ps.setString(i, parameter);

            if (logger.isDebugEnabled()) {
                logger.debug("AES存储字段，参数值长度: {}",
                    parameter != null ? parameter.length() : 0);
            }
        } catch (Exception e) {
            logger.error("AES存储失败，使用原值: {}", e.getMessage());
            ps.setString(i, parameter);
        }
    }
    
    /**
     * 根据列名获取结果时进行解密
     * 
     * @param rs 结果集
     * @param columnName 列名
     * @return 解密后的值
     * @throws SQLException SQL异常
     */
    @Override
    public String getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String encryptedValue = rs.getString(columnName);
        return decryptValue(encryptedValue);
    }
    
    /**
     * 根据列索引获取结果时进行解密
     * 
     * @param rs 结果集
     * @param columnIndex 列索引
     * @return 解密后的值
     * @throws SQLException SQL异常
     */
    @Override
    public String getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String encryptedValue = rs.getString(columnIndex);
        return decryptValue(encryptedValue);
    }
    
    /**
     * 从存储过程获取结果时进行解密
     * 
     * @param cs 可调用语句
     * @param columnIndex 列索引
     * @return 解密后的值
     * @throws SQLException SQL异常
     */
    @Override
    public String getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String encryptedValue = cs.getString(columnIndex);
        return decryptValue(encryptedValue);
    }
    
    /**
     * 解密值的通用方法
     *
     * @param encryptedValue 加密的值
     * @return 解密后的值
     */
    private String decryptValue(String encryptedValue) {
        if (encryptedValue == null) {
            return null;
        }

        try {
            // 直接返回数据库值（FieldEncryptionHelper会处理解密）
            return encryptedValue;
        } catch (Exception e) {
            logger.error("AES处理失败，返回原值: {}", e.getMessage());
            return encryptedValue;
        }
    }
}
