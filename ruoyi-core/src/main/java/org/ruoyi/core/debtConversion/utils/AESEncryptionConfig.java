package org.ruoyi.core.debtConversion.utils;

import org.apache.ibatis.session.SqlSessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * AES加解密自动配置类
 * 自动注册AES拦截器到MyBatis配置中
 * 
 * <AUTHOR>
 * @date 2024
 */
@Configuration
@ConditionalOnClass({SqlSessionFactory.class})
public class AESEncryptionConfig {
    
    private static final Logger logger = LoggerFactory.getLogger(AESEncryptionConfig.class);
    
    @Autowired
    private List<SqlSessionFactory> sqlSessionFactoryList;
    
    @Autowired
    private AESInterceptor aesInterceptor;
    
    /**
     * 初始化配置，将AES拦截器添加到所有SqlSessionFactory中
     */
    @PostConstruct
    public void addInterceptor() {
        try {
            for (SqlSessionFactory sqlSessionFactory : sqlSessionFactoryList) {
                sqlSessionFactory.getConfiguration().addInterceptor(aesInterceptor);
                logger.info("AES加解密拦截器已注册到SqlSessionFactory: {}", sqlSessionFactory.getClass().getSimpleName());
            }
            
            logger.info("AES加解密功能初始化完成，共注册 {} 个SqlSessionFactory", sqlSessionFactoryList.size());
        } catch (Exception e) {
            logger.error("AES加解密拦截器注册失败: {}", e.getMessage(), e);
        }
    }
}
