<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.debtConversion.mapper.DebtUserMapper">

    <resultMap type="DebtUser" id="DebtUserResult">
        <result property="id"    column="id"    />
        <result property="name"    column="name"    />
        <result property="phoneNum"    column="phone_num"    />
        <result property="idCard"    column="id_card"    />
        <result property="custId"    column="cust_id"    />
        <result property="realIdCard"    column="real_id_card"    />
        <result property="realPhoneNum"    column="real_phone_num"    />
        <result property="dataSource"    column="data_source"    />
        <result property="lastLoginTime"    column="last_login_time"    />
        <result property="openId"    column="open_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDebtUserVo">
        select id, name, phone_num, id_card, cust_id, data_source, create_by, create_time, update_by, last_login_time,update_time from dc_debt_user
    </sql>

    <select id="selectDebtUserList" parameterType="DebtUser" resultMap="DebtUserResult">
        select id, name
             , cust_id
             , id_card
             , phone_num
             , data_source
             , id_card as real_id_card
             , phone_num as real_phone_num
             , open_id
             , create_by, create_time, update_by, last_login_time,update_time
        from dc_debt_user

        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phoneNum != null  and phoneNum != ''"> and phone_num like concat('%', #{phoneNum}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card like concat('%', #{idCard}, '%')</if>
            <if test="custId != null "> and cust_id = #{custId}</if>
            <if test="openId != null "> and open_id = #{openId}</if>
            <if test="dataSource != null  and dataSource != ''"> and data_source = #{dataSource}</if>
            <if test="idCardList != null and idCardList.size() > 0">
                and id_card in
                <foreach collection="idCardList" item="idCard" separator="," open="(" close=")">
                    #{idCard}
                </foreach>
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectDebtUserListCount" parameterType="DebtUser" resultType="integer">
        select count(id)
        from dc_debt_user

        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phoneNum != null  and phoneNum != ''"> and phone_num like concat('%', #{phoneNum}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card like concat('%', #{idCard}, '%')</if>
            <if test="custId != null "> and cust_id = #{custId}</if>
            <if test="openId != null "> and open_id = #{openId}</if>
            <if test="dataSource != null  and dataSource != ''"> and data_source = #{dataSource}</if>
            <if test="idCardList != null and idCardList.size() > 0">
                and id_card in
                <foreach collection="idCardList" item="idCard" separator="," open="(" close=")">
                    #{idCard}
                </foreach>
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <select id="selectDebtUserById" parameterType="Long" resultMap="DebtUserResult">
        <include refid="selectDebtUserVo"/>
        where id = #{id}
    </select>

    <insert id="insertDebtUser" parameterType="DebtUser" useGeneratedKeys="true" keyProperty="id">
        insert into dc_debt_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="phoneNum != null">phone_num,</if>
            <if test="idCard != null">id_card,</if>
            <if test="custId != null">cust_id,</if>
            <if test="dataSource != null">data_source,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="openId != null">open_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="phoneNum != null">#{phoneNum},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="custId != null">#{custId},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="openId != null">#{openId},</if>
         </trim>
    </insert>

    <update id="updateDebtUser" parameterType="DebtUser">
        update dc_debt_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
<!--            <if test="phoneNum != null">phone_num = #{phoneNum},</if>-->
<!--            <if test="idCard != null">id_card = #{idCard},</if>-->
            <if test="custId != null">cust_id = #{custId},</if>
            <if test="dataSource != null">data_source = #{dataSource},</if>
            <if test="lastLoginTime != null">last_login_time = #{lastLoginTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="openId != null">open_id = #{openId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDebtUserById" parameterType="Long">
        delete from dc_debt_user where id = #{id}
    </delete>

    <delete id="deleteDebtUserByIds" parameterType="String">
        delete from dc_debt_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getDebtUser" parameterType="DebtUser" resultMap="DebtUserResult">
        select id, name, phone_num, id_card, cust_id, data_source
             , create_by, create_time, update_by, update_time
        from dc_debt_user
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phoneNum != null  and phoneNum != ''"> and phone_num = #{phoneNum}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="custId != null "> and cust_id = #{custId}</if>
            <if test="openId != null "> and open_id = #{openId}</if>
            <if test="dataSource != null  and dataSource != ''"> and data_source = #{dataSource}</if>
        </where>
        limit 1
    </select>
</mapper>
